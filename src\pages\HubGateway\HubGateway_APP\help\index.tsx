import { useState } from 'react';
import { Input, Button, Toast } from 'antd-mobile';
import { useHistory } from 'react-router-dom';
import styles from './index.module.scss';
import next_dark from "@/Resources/modal/next_dark.png";
import next from "@/Resources/modal/next.png";
import NavigatorBar from '@/components/NavBar';
import { useTheme } from '@/utils/themeDetector';
import { PreloadImage } from '@/components/Image';
import {
    CopyOutlined
} from '@ant-design/icons';

const Help = () => {
    const history = useHistory();
    const [ipAddress, setIpAddress] = useState('');
    const [loginCode, setLoginCode] = useState('');
    const { isDarkMode } = useTheme();


    const handleCopyLoginCode = () => {
        if (loginCode) {
            navigator.clipboard.writeText(loginCode).then(() => {
                Toast.show('已复制到剪贴板');
            }).catch(() => {
                Toast.show('复制失败');
            });
        }
    };

    const handleRefreshLoginCode = () => {
        // 这里应该调用API获取新的登录码
        // 暂时模拟生成一个随机登录码
        const newCode = Math.random().toString(36).substring(2, 15);
        setLoginCode(newCode);
        Toast.show('登录码已刷新');
    };

    const handleConnectStrategy = () => {
        // 跳转到连接攻略页面
        Toast.show('跳转到连接攻略');
    };

    const handleHelp = () => {
        // 跳转到使用帮助页面
        Toast.show('跳转到使用帮助');
    };

    return (
        <div className={styles.container}>
            <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} title='使用帮助' onBack={history.goBack} />

            <div className={styles.help}>
                {/* 使用帮助 */}
                <div className={styles.helpSection}>
                    <div className={styles.TitleBox}>
                        <span className={styles.title}>中枢与网关</span>
                        <PreloadImage src={isDarkMode ? next_dark : next} style={{ height: '10px', width: '10px' }} alt="next" />
                    </div>
                </div>
            </div>
            <div className={styles.help}>
                {/* 使用帮助 */}
                <div className={styles.helpSection}>
                    <div className={styles.TitleBox}>
                        <span className={styles.title}>自动化极客版</span>
                        <PreloadImage src={isDarkMode ? next_dark : next} style={{ height: '10px', width: '10px' }} alt="next" />
                    </div>
                </div>
            </div>
        </div>  
    );
};

export default Help;