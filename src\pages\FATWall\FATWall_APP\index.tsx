import { PreloadImage } from "@/components/Image";
import NavigatorBar from "@/components/NavBar";
import { FC, useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";
import { Route, Switch, useHistory, useRouteMatch } from "react-router-dom";
import searchIcon from "@/Resources/filmWall/search.png";
import moreIcon from "@/Resources/filmWall/more.png";
import searchIcon_dark from "@/Resources/filmWall/search_dark.png";
import moreIcon_dark from "@/Resources/filmWall/more_dark.png";
import filterIcon from "@/Resources/filmWall/filter.png";
import filterIcon_dark from "@/Resources/filmWall/filter_dark.png";
import { SearchOutline } from 'antd-mobile-icons';
import styles from "./index.module.scss";
import Recently from "./Recently";
import { Spin } from "antd";
import content_null_img from '@/Resources/filmWall/content_null.png';
import content_null_img_dark from '@/Resources/filmWall/content_null_dark.png';
import { useTheme } from '@/utils/themeDetector';
import All from "./All";
import { But<PERSON>, ErrorBlock, Input, Popover } from "antd-mobile";
import FilterFilmCard from "../../../components/FATWall_APP/FilterFilmCard";
import { searchLocalMedia } from "@/api/fatWall";
import { LibraryContext, useVideoLibraryList } from "@/hooks/useVideoLibrary";

const DefaultLayout = () => {
  const history = useHistory();

  useEffect(() => {
    history.push('/filmAndTelevisionWall_app/recently');
  }, [history])

  return (
    <div className={styles.loadingContainer}>
      <Spin size="large" />
    </div>
  )
}
// 搜索状态类型
type SearchStatus = 'idle' | 'loading' | 'success' | 'error' | 'empty';
// 搜索结果类型
interface SearchResult {
  id: string;
  title: string;
  year: string;
  country: string;
  genres: string;
  posterUrl: string;
  selected?: boolean;
  score: number;
  type: string;
  media_id: number;
  classes: string;
}


export const HomePage = (props: { path: string }) => {
  const { path } = props;
  const history = useHistory();
  const [showFloatPanel, setShowFloatPanel] = useState(false);
  const routeMatch = useRouteMatch();
  const prefix = routeMatch.path.split('/')[1];

  const [showMatchCorrection, setShowMatchCorrection] = useState<boolean>(false);
  const searchInputRef = useRef<any>(null);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [searchStatus, setSearchStatus] = useState<SearchStatus>('idle');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  // const [morePopoverVisible, setMorePopoverVisible] = useState<boolean>(false);
  const [moreVisible, setMoreVisible] = useState<boolean>(false);
  const [searchCategory, setSearchCategory] = useState<'全部' | '电影' | '电视剧'>('全部');

  const toSearch = useCallback(() => {
    setShowMatchCorrection(true);
    setSearchStatus('idle');
    setSearchResults([]);
    console.log('handleEditMatch');
    // 聚焦搜索框
    setTimeout(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 100);
  }, []);

  const toLibraryManagement = useCallback(() => {
    history.push(`/${prefix}/libraryManagement`);
  }, [history, prefix]);

  // 渲染搜索界面
  const renderMatchCorrection = () => {
    if (!showMatchCorrection) return null;

    return (
      <div className={styles.matchCorrectionOverlay}>
        <div className={styles.searchContainer}>
          <div className={styles.searchInputWrapper}>
            <div className={styles.searchIconWrapper}>
              <SearchOutline className={styles.searchIcon} />
            </div>
            <Input
              ref={searchInputRef}
              className={styles.searchInput}
              placeholder="请输入你要搜索的内容"
              value={searchKeyword}
              onChange={setSearchKeyword}
              onEnterPress={handleSearch}
              autoFocus
              clearable
            />
          </div>
          <div className={styles.cancelButton} onClick={handleCancelMatch}>
            取消
          </div>
        </div>
        <div className={styles.searchResults}>
          {renderSearchContent()}
        </div>
      </div>
    );

  };
  // 渲染搜索结果内容
  const renderSearchContent = () => {
    switch (searchStatus) {
      case 'loading':
        return (
          <div className={styles.searchStateContainer}>
            <div className={styles.loadingContainer}>
              <div className={styles.loadingSpinner}></div>
              <div className={styles.loadingText}>搜索中...</div>
            </div>
          </div>
        );
      case 'error':
        return (
          <>
            {/* Tab切换区域固定显示 */}
            <div className={styles.search_category_tabs_fixed}>
              <div
                className={`${styles.category_tab} ${searchCategory === '全部' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('全部')}
              >
                全部
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电影' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电影')}
              >
                电影
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电视剧' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电视剧')}
              >
                电视剧
              </div>
            </div>
            {/* 可滚动的搜索结果区域 */}
            <div className={styles.search_results_scrollable}>
              <div className={styles.searchStateContainer}>
                <ErrorBlock
                  className={styles.errorBlock}
                  image={isDarkMode ? content_null_img_dark : content_null_img}
                  status="default"
                  title="搜索失败，请重试"
                  description=""
                />
                <Button
                  className={styles.retryButton}
                  color="primary"
                  fill="none"
                  onClick={handleRetry}
                >
                  重试
                </Button>
              </div>
            </div>
          </>
        );
      case 'empty':
        return (
          <>
            {/* Tab切换区域固定显示 */}
            <div className={styles.search_category_tabs_fixed}>
              <div
                className={`${styles.category_tab} ${searchCategory === '全部' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('全部')}
              >
                全部
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电影' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电影')}
              >
                电影
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电视剧' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电视剧')}
              >
                电视剧
              </div>
            </div>
            {/* 可滚动的搜索结果区域 */}
            <div className={styles.search_results_scrollable}>
              <div className={styles.searchStateContainer}>
                <ErrorBlock
                  className={styles.errorBlock}
                  image={isDarkMode ? content_null_img_dark : content_null_img}
                  status="empty"
                  title="没搜索到相关结果"
                  description=""
                />
              </div>
            </div>
          </>
        );
      case 'success':
        return (
          <>
            {/* Tab切换区域固定显示 */}
            <div className={styles.search_category_tabs_fixed}>
              <div
                className={`${styles.category_tab} ${searchCategory === '全部' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('全部')}
              >
                全部
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电影' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电影')}
              >
                电影
              </div>
              <div
                className={`${styles.category_tab} ${searchCategory === '电视剧' ? styles.active_tab : ''}`}
                onClick={() => handleCategoryChange('电视剧')}
              >
                电视剧
              </div>
            </div>
            {/* 可滚动的搜索结果区域 */}
            <div className={styles.search_results_scrollable}>
              {filteredResults.length > 0 ? (
                <div className={styles.filter_films_container}>
                  {filteredResults.map(result => (
                    <FilterFilmCard
                      isLike={false}
                      key={result.id}
                      title={result.title}
                      subtitle={result.year}
                      score={result.score}
                      cover={result.posterUrl}
                      onCardClick={() => handleMovieCardClick(result)}
                    />
                  ))}
                </div>
              ) : (
                /* 当前分类无数据时显示空状态，但保持tab可见 */
                <div className={styles.searchStateContainer}>
                  <ErrorBlock
                    className={styles.errorBlock}
                    image=''
                    status="empty"
                    title="没搜索到相关结果"
                    description=""
                  />
                </div>
              )}
            </div>
          </>
        );
      default:
        return null;
    }
  };

  // 处理取消修改匹配
  const handleCancelMatch = useCallback(() => {
    setShowMatchCorrection(false);
    setSearchKeyword('');
    setSearchStatus('idle');
  }, []);
  // 处理搜索
  const handleSearch = useCallback(async () => {
    if (searchKeyword.trim()) {
      setSearchStatus('loading');

      try {
        // 根据搜索分类确定接口参数
        let classes = '';
        if (searchCategory === '电影') {
          classes = '电影';
        } else if (searchCategory === '电视剧') {
          classes = '电视剧';
        }

        const response = await searchLocalMedia({
          keyword: searchKeyword.trim(),
          filter: {
            offset: 0,
            limit: 50,
            classes
          }
        });

        if (response.code === 0 && response.data) {
          const { medias, count } = response.data;

          if (count === 0) {
            setSearchStatus('empty');
            setSearchResults([]);
          } else {
            // 将接口返回的mediaProps数据转换为SearchResult格式

            const transformedResults: SearchResult[] = medias.map(media => ({
              id: media.media_id.toString(),
              title: media.trans_name || media.origin_name || media.other_name,
              year: media.year.toString(),
              country: media.origin_place,
              genres: media.kind.join(' / '),
              posterUrl: media.poster.length > 0 ? media.poster[0] : '',
              type: media.classes === '电视剧' ? '电视剧' : '电影',
              media_id: media.media_id,
              classes: media.classes,
              // score: media.score ? media.score.toString() : '暂无评分',
              score: media.score ? media.score : 0,
            }));

            setSearchResults(transformedResults);
            setSearchStatus('success');
          }
        } else {
          setSearchStatus('error');
          setSearchResults([]);
        }
      } catch (error) {
        console.error('搜索失败:', error);
        setSearchStatus('error');
        setSearchResults([]);
      }
    }
  }, [searchKeyword, searchCategory]);
  // 处理重试
  const handleRetry = useCallback(() => {
    handleSearch();
  }, [handleSearch]);

  // 处理分类切换
  const handleCategoryChange = useCallback((category: '全部' | '电影' | '电视剧') => {
    setSearchCategory(category);
  }, []);

  // 当搜索分类改变且有搜索关键词时，重新搜索
  useEffect(() => {
    if (searchKeyword.trim() && (searchStatus === 'success' || searchStatus === 'empty' || searchStatus === 'error')) {
      handleSearch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchCategory]); // 只监听searchCategory变化

  // 根据当前分类筛选结果 - 现在接口已经根据分类返回了过滤后的结果
  const filteredResults = useMemo(() => {
    return searchResults;
  }, [searchResults]);

  // 处理点击电影卡片
  // const handleMovieCardClick = useCallback((item: any) => {
  //   // 关闭搜索界面
  //   setShowMatchCorrection(false);

  //   // 跳转到电影或电视剧详情页
  //   if (item.type === '电视剧') {
  //     history.push({ pathname: `/${prefix}/all/videoDetails`, state: { isDrama: true } });
  //   } else {
  //     history.push({ pathname: `/${prefix}/all/videoDetails`, state: { isDrama: false } });
  //   }
  // }, [history, prefix]);

  const handleMovieCardClick = useCallback((item: any) => {
    console.log('搜索结果点击项:', item);

    // 关闭搜索界面
    setShowMatchCorrection(false);

    // 将参数拼接到URL上，以便webView能够正确获取
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: (item.media_id || parseInt(item.id))?.toString() || '0',
      lib_id: '0'
    });

    history.push(`/filmAndTelevisionWall_app/all/videoDetails?${params.toString()}`);
  }, [history, setShowMatchCorrection])

  const { isDarkMode } = useTheme();

  const rightSize = useMemo(() => {
    return (
      <div className={styles.right}>
        <PreloadImage src={isDarkMode ? searchIcon_dark : searchIcon} alt="search" onClick={() => toSearch()} />
        {path === 'all' && (<PreloadImage src={isDarkMode ? filterIcon_dark : filterIcon} alt="filter" onClick={() => setShowFloatPanel(true)} />)}

        <Popover
          className={styles.morePopoverContainer}
          visible={moreVisible}
          onVisibleChange={setMoreVisible}
          content={
            <div className={styles.morePopover}>
              <div className={styles.morePopoverItem} onClick={toLibraryManagement}>
                <span className={styles.morePopoverText}>媒体库管理</span>
              </div>
            </div>
          }
          trigger='click'
          placement='bottom-end'
          style={{ '--arrow-size': '0px' } as React.CSSProperties}
        >

          <PreloadImage src={isDarkMode ? moreIcon_dark : moreIcon} alt="more" onClick={() => setMoreVisible(true)} />

        </Popover>
      </div>
    )
  }, [isDarkMode, path, moreVisible, toLibraryManagement, toSearch])

  return <>
    {renderMatchCorrection()}
    {!showMatchCorrection && (
      <>
        <NavigatorBar notBack right={rightSize} />
        <div className={styles.container}>
          <div className={styles.tabsHeader}>
            <span className={`${styles.tabsHeader_span} ${path === 'recently' ? styles.path_active : ''}`} onClick={() => history.push(`/${prefix}/recently`)}>最近</span>
            <span className={`${styles.tabsHeader_span} ${path === 'all' ? styles.path_active : ''}`} onClick={() => history.push(`/${prefix}/all`)}>全部</span>
          </div>
          {path === 'recently' ? <Recently /> : <All setShowFloatPanel={setShowFloatPanel} showFloatPanel={showFloatPanel} />}
        </div>
      </>
    )}


  </>
}

export const useLibraryListApp = () => useContext(LibraryContext);

const FilmAndTelevisionWallAPP: FC = (props) => {
  const { path } = useRouteMatch();

  const { libraries, getLib, setLibraries } = useVideoLibraryList();

  return (
    <LibraryContext.Provider value={{ libs: libraries, setLibs: setLibraries, refreshLibraries: getLib }}>
      <div className={styles.root_container}>
        {props.children}
        <Switch>
          {/* 默认内容 */}
          <Route exact={true} path={path}>
            <DefaultLayout />
          </Route>
        </Switch>
      </div>
    </LibraryContext.Provider>
  )
}

export default FilmAndTelevisionWallAPP;