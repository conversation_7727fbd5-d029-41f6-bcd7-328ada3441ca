import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import dataSelect from '@/Resources/player/dateSelect.png';
import refreshBtn from '@/Resources/layout/refreshBtn.png';
import allIcon from '@/Resources/layout/all_icon.png';
import selected from "@/Resources/icon/selected.png";
import notSelect from "@/Resources/icon/not_select.png";
import { DatePicker, ConfigProvider } from 'antd';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import type { Dayjs } from 'dayjs';
import PopoverSelector from '@/components/PopoverSelector';
import { cameraIconInfo } from '@/components/CameraPlayer/constants';
import { ICameraDetail } from '../../IPC_APP/CameraDetail';
import { ICollapsePanel } from '@/layouts/Layout';
import { getVideoRecord } from '@/api/ipc';
import { useCameras } from '..';
import { splitURL } from '@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer';
import { format } from 'date-fns';

dayjs.locale('zh-cn');

// 视频项接口
interface VideoItem {
  camera_lens: string;
  event_name: string;
  time: string;
  media_duration: number;
  file: string;
  create_time: string;
  cover_file: string;
  face_info: {
    uuid: string;
    name: string;
    profile_pic: string;
  }[];
  timeLabel?: string;
  thumbnail?: string;
}

export default function VideoList() {
  const { cameras } = useCameras();
  const [deviceDetail, setDeviceDetail] = useState<(ICollapsePanel & ICameraDetail)[]>([]);
  const [deviceIsShowSelector, setDeviceIsShowSelector] = useState<boolean>(false);
  const [curDeviceKey, setCurDeviceKey] = useState<string>('all');
  const [videoData, setVideoData] = useState<VideoItem[]>([]);
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(null); // 改为单个日期选择，初始为null
  const [hoverKey, setHoverKey] = useState<string>(''); // 悬浮key
  const [selectList, setSelectList] = useState<VideoItem[]>([]); // 已选择list
  const [loading, setLoading] = useState<boolean>(false);
  const pageRef = useRef<{ size: number, token: string }>({ size: 20, token: '' });

  // 设置设备详情
  useEffect(() => {
    if (cameras && cameras.length > 0) {
      setDeviceDetail(cameras.map(it => ({
        ...it,
        label: it.model,
        key: it.did,
        name: it.name,
        icon: cameraIconInfo(it.model)
      })));
    }
  }, [cameras]);

  // 当前设备
  const curDevice: (ICollapsePanel & ICameraDetail) | undefined = useMemo(() => {
    return deviceDetail.find((item) => item.key === curDeviceKey);
  }, [deviceDetail, curDeviceKey]);

  // 生成camera_lens参数
  const camera_lens = useMemo(() => {
    if (!deviceDetail.length) return [];
    const temp: string[] = [];
    if (curDeviceKey === 'all') {
      deviceDetail.forEach((it) => {
        it.key_frame.forEach((item) => temp.push(`${it.did}_${item.lens_id}`))
      });
    } else if (curDevice) {
      curDevice.key_frame.forEach((it) => {
        temp.push(`${curDevice.did}_${it.lens_id}`);
      });
    }
    return temp;
  }, [curDevice, curDeviceKey, deviceDetail]);

  // 设备选择选项
  const deviceSelectOptions = useMemo(() => {
    const ops = deviceDetail.map((item) => {
      return {
        label: item.name,
        value: item.key,
        icon: item.icon,
        subtitle: item.model
      };
    });
    ops.unshift({ label: '全部设备', value: 'all', icon: '', subtitle: '' });
    return ops;
  }, [deviceDetail]);

  // 获取视频数据
  const getData = useCallback(async () => {
    if (camera_lens.length === 0) return;

    setLoading(true);

    let params;

    if (selectedDate) {
      // 如果选择了日期，获取指定日期的视频数据
      const startOfDay = selectedDate.startOf('day');
      const endOfDay = selectedDate.endOf('day');

      params = {
        page: pageRef.current,
        options: {
          option: ["camera_lens", "time"],
          camera_lens: camera_lens,
          time: {
            start: Math.floor(startOfDay.valueOf() / 1000).toString(),
            end: Math.floor(endOfDay.valueOf() / 1000).toString(),
          },
        },
      };
    } else {
      // 如果没有选择日期，获取所有视频数据（不传时间参数）
      params = {
        page: pageRef.current,
        options: {
          option: ["camera_lens"],
          camera_lens: camera_lens,
        },
      };
    }

    try {
      const res = await getVideoRecord(params);
      if (res && res.code === 0 && res.data) {
        const videos = res.data.videos.map((video: any) => ({
          ...video,
          timeLabel: generateTimeRangeLabel(video.time, video.media_duration),
          thumbnail: video.cover_file ? `${video.cover_file}/original.jpg` : undefined,
        }));
        setVideoData(videos);
      }
    } catch (error) {
      console.error("获取视频数据失败:", error);
    } finally {
      setLoading(false);
    }
  }, [camera_lens, selectedDate]);

  // 生成时间范围标签
  const generateTimeRangeLabel = (startTimestamp: string | number, durationSeconds: number): string => {
    try {
      const startTime = typeof startTimestamp === 'string' ? parseInt(startTimestamp) : startTimestamp;
      const startDate = new Date(startTime);
      const endDate = new Date(startTime + (durationSeconds * 1000));

      const startTimeStr = format(startDate, 'HH:mm');
      const endTimeStr = format(endDate, 'HH:mm');

      return `${startTimeStr}-${endTimeStr}`;
    } catch (error) {
      console.error('时间范围标签生成失败:', error);
      return '00:00-00:00';
    }
  };

  // 当设备或日期改变时重新获取数据
  useEffect(() => {
    getData();
  }, [getData]);

  // 处理日期选择
  const handleDateChange = (date: Dayjs | null) => {
    setSelectedDate(date);
    pageRef.current = { size: 20, token: '' }; // 重置分页信息
  };

  // 选中回调，参考FATWall实现
  const clickCallback = useCallback((video: VideoItem) => {
    setSelectList((prev: VideoItem[]) => {
      let newList = [...prev];
      const isIncludes = newList.some(item =>
        item.camera_lens === video.camera_lens && item.time === video.time
      );
      if (isIncludes) {
        newList = newList.filter(item =>
          !(item.camera_lens === video.camera_lens && item.time === video.time)
        );
      } else {
        newList.push(video);
      }
      return newList;
    });
  }, []);

  // 处理全选/取消全选
  const handleSelectAll = () => {
    if (selectList.length === videoData.length) {
      setSelectList([]);
    } else {
      setSelectList([...videoData]);
    }
  };

  // 清空选择
  const clearSelection = () => {
    setSelectList([]);
    setHoverKey('');
  };

  // 刷新数据
  const handleRefresh = () => {
    clearSelection(); // 清空选择
    pageRef.current = { size: 20, token: '' }; // 重置分页信息
    getData();
  };

  // 格式化日期显示
  const dateDisplayText = useMemo(() => {
    if (!selectedDate) {
      return "选择日期"; // 未选择日期时的默认显示
    }
    const today = dayjs();
    const isToday = selectedDate.isSame(today, 'day');
    return isToday ? "今天" : selectedDate.format('M月DD日');
  }, [selectedDate]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.operation}>
          {selectList.length === 0 ? (
            <>
              {/* 设备选择器 */}
              <PopoverSelector
                visible={deviceIsShowSelector}
                onVisibleChange={setDeviceIsShowSelector}
                onChange={(value) => {
                  setCurDeviceKey(value);
                  pageRef.current = { size: 20, token: '' }; // 重置分页信息
                }}
                options={deviceSelectOptions}
                value={curDevice ? curDevice.key : 'all'}
              >
                <div className={styles.operationItem}>
                  <span>{curDevice ? curDevice.name : '全部设备'}</span>
                  <PreloadImage src={dataSelect} alt='select' />
                </div>
              </PopoverSelector>

              {/* 日期选择器 */}
              <div className={styles.operationItem}>
                <ConfigProvider locale={locale}>
                  <DatePicker
                    placeholder={dateDisplayText}
                    value={selectedDate}
                    onChange={handleDateChange}
                    disabledDate={(current) => current && current > dayjs().endOf('day')}
                    allowClear
                    suffixIcon={<PreloadImage src={dataSelect} alt='select' />}
                  />
                </ConfigProvider>
              </div>

              {/* 全选按钮 */}
              <div className={styles.operationItem} onClick={handleSelectAll}>
                <PreloadImage src={allIcon} alt='select-all' />
                <span>全选</span>
              </div>

              {/* 刷新按钮 */}
              <div className={styles.operationItem} onClick={handleRefresh}>
                <PreloadImage src={refreshBtn} alt='refresh' />
                <span>刷新</span>
              </div>
            </>
          ) : (
            <>
              {/* 选中状态的操作按钮 */}
              <div className={styles.operationItem} onClick={clearSelection}>
                <span>取消选择</span>
              </div>

              <div className={styles.operationItem} onClick={handleSelectAll}>
                <PreloadImage src={allIcon} alt='select-all' />
                <span>{selectList.length === videoData.length ? '取消全选' : '全选'}</span>
              </div>

              <div className={styles.operationItem}>
                <span>下载选中 ({selectList.length})</span>
              </div>

              <div className={styles.operationItem}>
                <span>删除选中</span>
              </div>
            </>
          )}
        </div>
      </div>

      <div className={styles.content}>
        {/* 日期标题 */}
        {selectedDate && (
          <div className={styles.dateTitle}>
            {selectedDate.format('YYYY年M月DD日')} | {selectedDate.isSame(dayjs(), 'day') ? '今天' : selectedDate.format('dddd')}
          </div>
        )}

        {loading ? (
          <div className={styles.loading}>
            <span className={styles.loadingText}>加载中...</span>
          </div>
        ) : videoData.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>📹</div>
            <div className={styles.emptyText}>暂无视频数据</div>
          </div>
        ) : (
          <div className={styles.videoGrid}>
            {videoData.map((video, index) => {
              const videoKey = `${video.camera_lens}-${video.time}-${index}`;
              const isSelected = selectList.some(item =>
                item.camera_lens === video.camera_lens && item.time === video.time
              );

              return (
                <div
                  key={videoKey}
                  className={styles.videoItem}
                  style={{ background: isSelected ? 'var(--fat-card-hover-bg)' : '' }}
                  onMouseLeave={() => setHoverKey('')}
                  onMouseEnter={() => setHoverKey(videoKey)}
                  onClick={() => {
                    // 这里可以添加视频播放逻辑
                    console.log('播放视频:', video);
                  }}
                >
                  <div className={styles.thumbnailContainer}>
                    {video.thumbnail ? (
                      <PreloadImage
                        src={splitURL(video.thumbnail)}
                        alt=""
                        className={styles.thumbnailImage}
                        needHeader={true}
                      />
                    ) : (
                      <div className={styles.thumbnailPlaceholder}>
                        <span>暂无缩略图</span>
                      </div>
                    )}

                    {/* 选择框，参考FATWall实现 */}
                    <div
                      className={styles.selectItem}
                      onClick={(e) => {
                        e.stopPropagation();
                        clickCallback(video);
                      }}
                      style={{
                        visibility: (isSelected || hoverKey === videoKey) ? 'visible' : 'hidden'
                      }}
                    >
                      {isSelected ?
                        <PreloadImage src={selected} alt="selected" /> :
                        <PreloadImage src={notSelect} alt="notSelect" />
                      }
                    </div>
                  </div>
                  <div className={styles.timeLabel}>
                    {video.timeLabel || format(new Date(parseInt(video.time) * 1000), 'HH:mm')}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>


    </div>
  );
}
