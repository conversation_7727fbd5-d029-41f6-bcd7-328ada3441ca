import { useState } from 'react';
import { Input, Button, Toast } from 'antd-mobile';
import { Route, Switch, useHistory, useRouteMatch } from 'react-router-dom';
import styles from './index.module.scss';
import next_dark from "@/Resources/modal/next_dark.png";
import next from "@/Resources/modal/next.png";
import NavigatorBar from '@/components/NavBar';
import { useTheme } from '@/utils/themeDetector';
import { PreloadImage } from '@/components/Image';
import {
    CopyOutlined
} from '@ant-design/icons';
import Help from './Help';

const HubGateway = () => {
    const history = useHistory();
    const { path } = useRouteMatch();
    const [ipAddress, setIpAddress] = useState('');
    const [loginCode, setLoginCode] = useState('');
    const { isDarkMode } = useTheme();


    const handleCopyLoginCode = () => {
        if (loginCode) {
            navigator.clipboard.writeText(loginCode).then(() => {
                Toast.show('已复制到剪贴板');
            }).catch(() => {
                Toast.show('复制失败');
            });
        }
    };

    const handleRefreshLoginCode = () => {
        // 这里应该调用API获取新的登录码
        // 暂时模拟生成一个随机登录码
        const newCode = Math.random().toString(36).substring(2, 15);
        setLoginCode(newCode);
        Toast.show('登录码已刷新');
    };

    const handleConnectStrategy = () => {
        // 跳转到连接攻略页面
        Toast.show('跳转到连接攻略');
    };

    const handleHelp = () => {
        console.log('====================================');
        console.log(123123);
        console.log('====================================');
        history.push('/hubGateway_app/help')
    };

    return (
        <div className={styles.container}>
            <Switch>
                <Route exact path={path}>
                    <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} title='中枢网关' onBack={history.goBack} />

                    <div className={styles.content}>
                        {/* 自动化极客版 */}
                        <div className={styles.section}>
                            <div className={styles.TitleBox}>
                                <span className={styles.title}>自动化极客版</span>
                                <div>
                                    <span className={styles.titleTwo}>连接攻略</span>
                                    <PreloadImage src={isDarkMode ? next_dark : next} style={{ height: '10px', width: '10px' }} alt="next" />
                                </div>

                            </div>
                            {/* IP地址输入 */}
                            <div className={styles.inputSection}>
                                <div className={styles.titleTwo}>IP地址</div>
                                <div className={styles.inputWrapper}>
                                    <Input
                                        placeholder="请输入IP地址"
                                        value={ipAddress}
                                        onChange={setIpAddress}
                                        className={styles.input}
                                    />


                                </div>

                            </div>

                            {/* 登录码输入 */}
                            <div className={styles.inputSection}>
                                <div className={styles.titleTwo}>登录码</div>
                                <div className={styles.inputWrapper}>
                                    <Input
                                        placeholder="请输入登录码"
                                        value={loginCode}
                                        onChange={setLoginCode}
                                        className={styles.input}
                                    />
                                    <div
                                        className={styles.copyIcon}
                                        onClick={handleCopyLoginCode}
                                    >
                                        <CopyOutlined />
                                    </div>
                                </div>
                            </div>

                            {/* 重新获取登录码按钮 */}
                            <div className={styles.buttonSection}>
                                <Button
                                    color="primary"
                                    size="large"
                                    onClick={handleRefreshLoginCode}
                                    className={styles.refreshButton}
                                >
                                    重新获取登录码
                                </Button>
                            </div>
                        </div>
                    </div>
                    <div className={styles.help} onClick={handleHelp}>
                        {/* 使用帮助 */}
                        <div className={styles.helpSection}>
                            <div className={styles.Boxtwo}>
                                <span className={styles.title}>使用帮助</span>
                                <PreloadImage src={isDarkMode ? next_dark : next} style={{ height: '10px', width: '10px' }} alt="next" />
                            </div>
                        </div>
                    </div>
                </Route>
                <Route path={`${path}/help`}>
                    <Help />
                </Route>
            </Switch>
        </div>
    );
};

export default HubGateway;