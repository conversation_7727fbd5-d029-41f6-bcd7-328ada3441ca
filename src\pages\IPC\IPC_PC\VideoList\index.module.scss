.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background-color: #f5f5f5;
}

.header {
  margin-bottom: 20px;
}

.operation {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
  padding: 16px 0;
}

.operationItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  span {
    font-size: 14px;
    color: #333;
  }

  img {
    width: 16px;
    height: 16px;
  }

  // DatePicker 样式
  :global(.ant-picker) {
    border: none;
    background: transparent;
    box-shadow: none;
    padding: 0;

    .ant-picker-input > input {
      font-size: 14px;
      color: #333;
    }

    &:hover, &:focus {
      border-color: transparent;
      box-shadow: none;
    }
  }
}

.operationItem_datePicker {
  :global(.ant-picker) {
    border-radius: 6px;
  }
}

.content {
  flex: 1;
  overflow-y: auto;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dateTitle {
  font-size: 16px;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.videoGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16px;
  padding: 10px 0;
}

.videoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.thumbnailContainer {
  position: relative;
  width: 120px;
  height: 90px;
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selectItem {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 4px;
  right: 4px;
  visibility: hidden;
  background-color: var(--modal-content-background-color);
  border-radius: 8px;

  &:hover {
    cursor: pointer;
  }

  img {
    height: 16px;
  }
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.thumbnailPlaceholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;

  span {
    color: #999;
    font-size: 12px;
  }
}

.timeLabel {
  font-size: 12px;
  color: #666;
  text-align: center;
  font-weight: 500;
}

// 空状态样式
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #999;

  .emptyIcon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .emptyText {
    font-size: 16px;
  }
}

// 加载状态
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .loadingText {
    color: #666;
    font-size: 14px;
  }
}

